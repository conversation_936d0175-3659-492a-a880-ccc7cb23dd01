"""
Provider performance monitoring and comparison.

This module tracks the performance of different STT, TTS, and translation providers
to enable intelligent provider selection and performance optimization.
"""

import time
import asyncio
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from collections import defaultdict, deque
from statistics import mean, median

from .metrics import voice_metrics
from ..core.config import settings


@dataclass
class ProviderStats:
    """Statistics for a provider."""
    
    # Performance metrics
    latencies: deque = field(default_factory=lambda: deque(maxlen=100))
    success_count: int = 0
    failure_count: int = 0
    total_requests: int = 0
    
    # Quality metrics
    confidence_scores: deque = field(default_factory=lambda: deque(maxlen=100))
    quality_scores: deque = field(default_factory=lambda: deque(maxlen=100))
    
    # Cost tracking
    estimated_cost: float = 0.0
    
    # Availability tracking
    last_success_time: Optional[float] = None
    last_failure_time: Optional[float] = None
    consecutive_failures: int = 0
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate."""
        if self.total_requests == 0:
            return 1.0
        return self.success_count / self.total_requests
    
    @property
    def average_latency(self) -> float:
        """Calculate average latency."""
        if not self.latencies:
            return 0.0
        return mean(self.latencies)
    
    @property
    def median_latency(self) -> float:
        """Calculate median latency."""
        if not self.latencies:
            return 0.0
        return median(self.latencies)
    
    @property
    def average_confidence(self) -> float:
        """Calculate average confidence score."""
        if not self.confidence_scores:
            return 0.0
        return mean(self.confidence_scores)
    
    @property
    def average_quality(self) -> float:
        """Calculate average quality score."""
        if not self.quality_scores:
            return 0.0
        return mean(self.quality_scores)
    
    @property
    def availability(self) -> float:
        """Calculate availability score."""
        if self.consecutive_failures > 5:
            return 0.0
        elif self.consecutive_failures > 2:
            return 0.5
        elif self.success_rate < 0.8:
            return 0.7
        else:
            return 1.0


class ProviderPerformanceMonitor:
    """Monitors and compares provider performance."""
    
    def __init__(self):
        self.stats: Dict[str, Dict[str, ProviderStats]] = {
            "stt": defaultdict(ProviderStats),
            "tts": defaultdict(ProviderStats),
            "translation": defaultdict(ProviderStats),
            "vad": defaultdict(ProviderStats)
        }
        self.performance_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # Provider cost estimates (per request)
        self.cost_estimates = {
            "stt": {
                "whisper": 0.0,  # Free/local
                "deepgram": 0.0043,  # $0.0043 per minute
                "openai": 0.006,  # $0.006 per minute
            },
            "tts": {
                "openai": 0.015,  # $0.015 per 1K characters
                "deepgram": 0.0135,  # $0.0135 per 1K characters
            },
            "translation": {
                "huggingface": 0.0,  # Free/local
                "google": 0.02,  # $20 per 1M characters
                "azure": 0.01,  # $10 per 1M characters
            }
        }
    
    def record_request_start(self, provider_type: str, provider_name: str) -> str:
        """Record the start of a provider request."""
        request_id = f"{provider_type}_{provider_name}_{int(time.time() * 1000)}"
        return request_id
    
    def record_request_success(
        self,
        provider_type: str,
        provider_name: str,
        latency: float,
        confidence: Optional[float] = None,
        quality_score: Optional[float] = None,
        data_size: Optional[int] = None
    ) -> None:
        """Record a successful provider request."""
        stats = self.stats[provider_type][provider_name]
        
        # Update basic stats
        stats.latencies.append(latency)
        stats.success_count += 1
        stats.total_requests += 1
        stats.last_success_time = time.time()
        stats.consecutive_failures = 0
        
        # Update quality metrics
        if confidence is not None:
            stats.confidence_scores.append(confidence)
        if quality_score is not None:
            stats.quality_scores.append(quality_score)
        
        # Update cost estimate
        if data_size and provider_type in self.cost_estimates:
            if provider_name in self.cost_estimates[provider_type]:
                cost_per_unit = self.cost_estimates[provider_type][provider_name]
                if provider_type == "stt":
                    # Cost per minute of audio
                    audio_minutes = data_size / (16000 * 2 * 60)  # 16kHz, 16-bit
                    stats.estimated_cost += cost_per_unit * audio_minutes
                elif provider_type == "tts":
                    # Cost per 1K characters
                    stats.estimated_cost += cost_per_unit * (data_size / 1000)
                elif provider_type == "translation":
                    # Cost per 1M characters
                    stats.estimated_cost += cost_per_unit * (data_size / 1000000)
        
        # Record metrics
        voice_metrics.record_provider_availability(
            provider_type=provider_type,
            provider_name=provider_name,
            availability=stats.availability
        )
        
        if confidence is not None:
            voice_metrics.record_provider_performance(
                provider_type=provider_type,
                provider_name=provider_name,
                metric_type="confidence",
                score=confidence
            )
        
        # Record latency performance score (inverse of latency, normalized)
        latency_score = max(0.0, min(1.0, 1.0 - (latency / 10.0)))  # 10s = 0 score
        voice_metrics.record_provider_performance(
            provider_type=provider_type,
            provider_name=provider_name,
            metric_type="latency",
            score=latency_score
        )
    
    def record_request_failure(
        self,
        provider_type: str,
        provider_name: str,
        error_type: str,
        error_message: str
    ) -> None:
        """Record a failed provider request."""
        stats = self.stats[provider_type][provider_name]
        
        # Update basic stats
        stats.failure_count += 1
        stats.total_requests += 1
        stats.last_failure_time = time.time()
        stats.consecutive_failures += 1
        
        # Record error metrics
        voice_metrics.record_processing_error(
            component=provider_type,
            provider=provider_name,
            error_type=error_type
        )
        
        voice_metrics.record_provider_failure(
            provider_type=provider_type,
            provider_name=provider_name,
            failure_reason=error_type
        )
        
        # Update availability
        voice_metrics.record_provider_availability(
            provider_type=provider_type,
            provider_name=provider_name,
            availability=stats.availability
        )
    
    def get_provider_ranking(self, provider_type: str) -> List[Tuple[str, float]]:
        """Get providers ranked by performance score."""
        rankings = []
        
        for provider_name, stats in self.stats[provider_type].items():
            if stats.total_requests == 0:
                continue
            
            # Calculate composite performance score
            success_weight = 0.4
            latency_weight = 0.3
            quality_weight = 0.2
            availability_weight = 0.1
            
            success_score = stats.success_rate
            latency_score = max(0.0, min(1.0, 1.0 - (stats.average_latency / 10.0)))
            quality_score = stats.average_quality if stats.quality_scores else stats.average_confidence
            availability_score = stats.availability
            
            composite_score = (
                success_score * success_weight +
                latency_score * latency_weight +
                quality_score * quality_weight +
                availability_score * availability_weight
            )
            
            rankings.append((provider_name, composite_score))
        
        return sorted(rankings, key=lambda x: x[1], reverse=True)
    
    def get_best_provider(self, provider_type: str) -> Optional[str]:
        """Get the best performing provider for a given type."""
        rankings = self.get_provider_ranking(provider_type)
        if rankings:
            return rankings[0][0]
        return None
    
    def should_switch_provider(
        self,
        provider_type: str,
        current_provider: str,
        failure_threshold: int = 3
    ) -> Optional[str]:
        """Determine if we should switch providers due to poor performance."""
        current_stats = self.stats[provider_type].get(current_provider)
        if not current_stats:
            return None
        
        # Check if current provider is failing too much
        if current_stats.consecutive_failures >= failure_threshold:
            best_alternative = self.get_best_provider(provider_type)
            if best_alternative and best_alternative != current_provider:
                return best_alternative
        
        return None
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get a summary of all provider performance."""
        summary = {}
        
        for provider_type, providers in self.stats.items():
            summary[provider_type] = {}
            for provider_name, stats in providers.items():
                if stats.total_requests > 0:
                    summary[provider_type][provider_name] = {
                        "total_requests": stats.total_requests,
                        "success_rate": stats.success_rate,
                        "average_latency": stats.average_latency,
                        "median_latency": stats.median_latency,
                        "average_confidence": stats.average_confidence,
                        "average_quality": stats.average_quality,
                        "availability": stats.availability,
                        "estimated_cost": stats.estimated_cost,
                        "consecutive_failures": stats.consecutive_failures
                    }
        
        return summary


# Global provider monitor instance
provider_monitor = ProviderPerformanceMonitor()
