"""
Voice Gateway specific metrics for monitoring pipeline performance.

This module provides detailed metrics for the speech-to-speech translation pipeline,
including latency, throughput, error rates, and provider-specific performance metrics.
"""

import time
from typing import Dict, Any, Optional
from cortexacommon.monitoring.metrics import create_histogram, create_counter, create_gauge
from ..core.config import settings

# Pipeline latency metrics (histograms for percentile analysis)
pipeline_latency = create_histogram(
    "voice_gateway_pipeline_latency_seconds",
    "Total pipeline processing latency from audio input to output",
    labels=["call_id", "provider_combination"],
    buckets=[0.1, 0.25, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0]
)

# Component-specific latency metrics
stt_latency = create_histogram(
    "voice_gateway_stt_latency_seconds", 
    "Speech-to-text processing latency",
    labels=["provider", "model", "audio_duration_bucket"],
    buckets=[0.05, 0.1, 0.25, 0.5, 1.0, 2.0, 5.0, 10.0]
)

translation_latency = create_histogram(
    "voice_gateway_translation_latency_seconds",
    "Translation processing latency", 
    labels=["provider", "model", "text_length_bucket"],
    buckets=[0.01, 0.05, 0.1, 0.25, 0.5, 1.0, 2.0]
)

tts_latency = create_histogram(
    "voice_gateway_tts_latency_seconds",
    "Text-to-speech processing latency",
    labels=["provider", "model", "voice", "text_length_bucket"], 
    buckets=[0.1, 0.25, 0.5, 1.0, 2.0, 5.0, 10.0]
)

vad_latency = create_histogram(
    "voice_gateway_vad_latency_seconds",
    "Voice activity detection latency",
    labels=["provider", "aggressiveness"],
    buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1]
)

# Throughput metrics
audio_throughput = create_histogram(
    "voice_gateway_audio_throughput_bytes_per_second",
    "Audio processing throughput",
    labels=["direction", "call_id"],
    buckets=[1000, 5000, 10000, 25000, 50000, 100000, 250000]
)

# Quality metrics
stt_confidence = create_histogram(
    "voice_gateway_stt_confidence_score",
    "STT confidence scores",
    labels=["provider", "model"],
    buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 0.95, 0.99]
)

# Real-time factor metrics (how fast processing is compared to audio duration)
realtime_factor = create_histogram(
    "voice_gateway_realtime_factor",
    "Processing time divided by audio duration (lower is better)",
    labels=["component", "provider"],
    buckets=[0.1, 0.25, 0.5, 0.75, 1.0, 1.5, 2.0, 3.0, 5.0, 10.0]
)

# Error counters
processing_errors = create_counter(
    "voice_gateway_processing_errors_total",
    "Total processing errors by component and type",
    labels=["component", "provider", "error_type"]
)

provider_failures = create_counter(
    "voice_gateway_provider_failures_total", 
    "Provider-specific failure counts",
    labels=["provider_type", "provider_name", "failure_reason"]
)

# Connection and session metrics
active_calls = create_gauge(
    "voice_gateway_active_calls",
    "Number of active voice calls"
)

segments_processed = create_counter(
    "voice_gateway_segments_processed_total",
    "Total speech segments processed",
    labels=["call_id", "provider_combination"]
)

# Queue metrics
queue_size = create_gauge(
    "voice_gateway_queue_size",
    "Current queue sizes",
    labels=["queue_type", "call_id"]
)

queue_wait_time = create_histogram(
    "voice_gateway_queue_wait_time_seconds",
    "Time items spend waiting in queues",
    labels=["queue_type", "call_id"],
    buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0]
)

# Provider switching metrics
provider_switches = create_counter(
    "voice_gateway_provider_switches_total",
    "Number of provider switches due to failures",
    labels=["provider_type", "from_provider", "to_provider", "reason"]
)

# Provider performance comparison metrics
provider_performance_score = create_histogram(
    "voice_gateway_provider_performance_score",
    "Provider performance score (0-1, higher is better)",
    labels=["provider_type", "provider_name", "metric_type"],
    buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 0.95, 0.99]
)

provider_availability = create_gauge(
    "voice_gateway_provider_availability",
    "Provider availability (0-1, 1 = fully available)",
    labels=["provider_type", "provider_name"]
)

provider_cost_per_request = create_histogram(
    "voice_gateway_provider_cost_per_request",
    "Estimated cost per request by provider",
    labels=["provider_type", "provider_name"],
    buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5]
)

# Provider-specific feature metrics
stt_word_error_rate = create_histogram(
    "voice_gateway_stt_word_error_rate",
    "Estimated word error rate for STT providers",
    labels=["provider", "model", "language"],
    buckets=[0.01, 0.02, 0.05, 0.1, 0.15, 0.2, 0.3, 0.5]
)

tts_naturalness_score = create_histogram(
    "voice_gateway_tts_naturalness_score",
    "TTS naturalness score (0-1, higher is better)",
    labels=["provider", "model", "voice"],
    buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 0.95]
)

translation_bleu_score = create_histogram(
    "voice_gateway_translation_bleu_score",
    "Translation BLEU score (0-1, higher is better)",
    labels=["provider", "model", "language_pair"],
    buckets=[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 0.95]
)


class VoiceGatewayMetrics:
    """Voice Gateway metrics collector and helper class."""
    
    def __init__(self):
        self.call_start_times: Dict[str, float] = {}
        self.segment_start_times: Dict[str, float] = {}
    
    def record_call_started(self, call_id: str) -> None:
        """Record when a call starts."""
        self.call_start_times[call_id] = time.time()
        if active_calls:
            active_calls.inc()
    
    def record_call_ended(self, call_id: str) -> None:
        """Record when a call ends."""
        if call_id in self.call_start_times:
            del self.call_start_times[call_id]
        if active_calls:
            active_calls.dec()
    
    def record_segment_processing_start(self, segment_id: str) -> None:
        """Record when segment processing starts."""
        self.segment_start_times[segment_id] = time.time()
    
    def record_pipeline_latency(
        self, 
        call_id: str, 
        duration: float,
        stt_provider: str,
        translation_provider: str, 
        tts_provider: str
    ) -> None:
        """Record total pipeline processing latency."""
        provider_combo = f"{stt_provider}+{translation_provider}+{tts_provider}"
        if pipeline_latency:
            pipeline_latency.labels(
                call_id=call_id,
                provider_combination=provider_combo
            ).observe(duration)
    
    def record_stt_metrics(
        self,
        provider: str,
        model: str,
        duration: float,
        confidence: float,
        audio_duration: float
    ) -> None:
        """Record STT-specific metrics."""
        audio_bucket = self._get_audio_duration_bucket(audio_duration)
        
        if stt_latency:
            stt_latency.labels(
                provider=provider,
                model=model,
                audio_duration_bucket=audio_bucket
            ).observe(duration)
        
        if stt_confidence:
            stt_confidence.labels(
                provider=provider,
                model=model
            ).observe(confidence)
        
        if realtime_factor:
            rt_factor = duration / audio_duration if audio_duration > 0 else 0
            realtime_factor.labels(
                component="stt",
                provider=provider
            ).observe(rt_factor)
    
    def record_translation_metrics(
        self,
        provider: str,
        model: str,
        duration: float,
        text_length: int
    ) -> None:
        """Record translation-specific metrics."""
        text_bucket = self._get_text_length_bucket(text_length)
        
        if translation_latency:
            translation_latency.labels(
                provider=provider,
                model=model,
                text_length_bucket=text_bucket
            ).observe(duration)
    
    def record_tts_metrics(
        self,
        provider: str,
        model: str,
        voice: str,
        duration: float,
        text_length: int
    ) -> None:
        """Record TTS-specific metrics."""
        text_bucket = self._get_text_length_bucket(text_length)
        
        if tts_latency:
            tts_latency.labels(
                provider=provider,
                model=model,
                voice=voice,
                text_length_bucket=text_bucket
            ).observe(duration)
    
    def record_vad_metrics(
        self,
        provider: str,
        aggressiveness: int,
        duration: float
    ) -> None:
        """Record VAD-specific metrics."""
        if vad_latency:
            vad_latency.labels(
                provider=provider,
                aggressiveness=str(aggressiveness)
            ).observe(duration)
    
    def record_processing_error(
        self,
        component: str,
        provider: str,
        error_type: str
    ) -> None:
        """Record a processing error."""
        if processing_errors:
            processing_errors.labels(
                component=component,
                provider=provider,
                error_type=error_type
            ).inc()
    
    def record_provider_failure(
        self,
        provider_type: str,
        provider_name: str,
        failure_reason: str
    ) -> None:
        """Record a provider failure."""
        if provider_failures:
            provider_failures.labels(
                provider_type=provider_type,
                provider_name=provider_name,
                failure_reason=failure_reason
            ).inc()
    
    def record_queue_metrics(
        self,
        queue_type: str,
        call_id: str,
        size: int,
        wait_time: Optional[float] = None
    ) -> None:
        """Record queue metrics."""
        if queue_size:
            queue_size.labels(
                queue_type=queue_type,
                call_id=call_id
            ).set(size)
        
        if wait_time is not None and queue_wait_time:
            queue_wait_time.labels(
                queue_type=queue_type,
                call_id=call_id
            ).observe(wait_time)
    
    def record_segment_processed(
        self,
        call_id: str,
        stt_provider: str,
        translation_provider: str,
        tts_provider: str
    ) -> None:
        """Record a completed segment."""
        provider_combo = f"{stt_provider}+{translation_provider}+{tts_provider}"
        if segments_processed:
            segments_processed.labels(
                call_id=call_id,
                provider_combination=provider_combo
            ).inc()
    
    def _get_audio_duration_bucket(self, duration: float) -> str:
        """Get audio duration bucket for labeling."""
        if duration < 1.0:
            return "short"
        elif duration < 5.0:
            return "medium"
        elif duration < 15.0:
            return "long"
        else:
            return "very_long"
    
    def _get_text_length_bucket(self, length: int) -> str:
        """Get text length bucket for labeling."""
        if length < 50:
            return "short"
        elif length < 200:
            return "medium"
        elif length < 500:
            return "long"
        else:
            return "very_long"

    def record_provider_performance(
        self,
        provider_type: str,
        provider_name: str,
        metric_type: str,
        score: float
    ) -> None:
        """Record provider performance score."""
        if provider_performance_score:
            provider_performance_score.labels(
                provider_type=provider_type,
                provider_name=provider_name,
                metric_type=metric_type
            ).observe(score)

    def record_provider_availability(
        self,
        provider_type: str,
        provider_name: str,
        availability: float
    ) -> None:
        """Record provider availability."""
        if provider_availability:
            provider_availability.labels(
                provider_type=provider_type,
                provider_name=provider_name
            ).set(availability)

    def record_stt_quality_metrics(
        self,
        provider: str,
        model: str,
        language: str,
        word_error_rate: Optional[float] = None,
        confidence: Optional[float] = None
    ) -> None:
        """Record STT quality metrics."""
        if word_error_rate is not None and stt_word_error_rate:
            stt_word_error_rate.labels(
                provider=provider,
                model=model,
                language=language
            ).observe(word_error_rate)

        if confidence is not None:
            # Convert confidence to performance score
            self.record_provider_performance(
                provider_type="stt",
                provider_name=provider,
                metric_type="confidence",
                score=confidence
            )

    def record_tts_quality_metrics(
        self,
        provider: str,
        model: str,
        voice: str,
        naturalness_score: Optional[float] = None,
        audio_quality: Optional[float] = None
    ) -> None:
        """Record TTS quality metrics."""
        if naturalness_score is not None and tts_naturalness_score:
            tts_naturalness_score.labels(
                provider=provider,
                model=model,
                voice=voice
            ).observe(naturalness_score)

        if audio_quality is not None:
            self.record_provider_performance(
                provider_type="tts",
                provider_name=provider,
                metric_type="audio_quality",
                score=audio_quality
            )

    def record_translation_quality_metrics(
        self,
        provider: str,
        model: str,
        language_pair: str,
        bleu_score: Optional[float] = None,
        fluency_score: Optional[float] = None
    ) -> None:
        """Record translation quality metrics."""
        if bleu_score is not None and translation_bleu_score:
            translation_bleu_score.labels(
                provider=provider,
                model=model,
                language_pair=language_pair
            ).observe(bleu_score)

        if fluency_score is not None:
            self.record_provider_performance(
                provider_type="translation",
                provider_name=provider,
                metric_type="fluency",
                score=fluency_score
            )

    def record_provider_switch(
        self,
        provider_type: str,
        from_provider: str,
        to_provider: str,
        reason: str
    ) -> None:
        """Record a provider switch event."""
        if provider_switches:
            provider_switches.labels(
                provider_type=provider_type,
                from_provider=from_provider,
                to_provider=to_provider,
                reason=reason
            ).inc()


# Global metrics instance
voice_metrics = VoiceGatewayMetrics()
