"""
WebRTC VAD provider implementation.
"""

import logging
from typing import Optional

from .base import BaseVADProvider
from ...core.config import settings

logger = logging.getLogger(__name__)


class WebRTCVADProvider(BaseVADProvider):
    """Voice Activity Detection provider using WebRTC VAD."""
    
    def __init__(self, aggressiveness: int = 3, frame_duration_ms: int = 30, **kwargs):
        """
        Initialize WebRTC VAD provider.
        
        Args:
            aggressiveness: VAD aggressiveness level (0-3)
            frame_duration_ms: Frame duration in milliseconds
            **kwargs: Additional configuration parameters
        """
        super().__init__(**kwargs)
        self.aggressiveness = aggressiveness
        self.frame_duration_ms = frame_duration_ms
        self._frame_size = int(settings.audio_sample_rate * frame_duration_ms / 1000)
        self._vad: Optional[object] = None
    
    async def initialize(self) -> None:
        """Initialize WebRTC VAD."""
        try:
            import webrtcvad
            self._vad = webrtcvad.Vad(self.aggressiveness)
            self._initialized = True
            logger.info(f"WebRTC VAD initialized with aggressiveness {self.aggressiveness}")
        except ImportError:
            logger.error("webrtcvad not available, VAD disabled")
            self._vad = None
            self._initialized = False
        except Exception as e:
            logger.error(f"Failed to initialize WebRTC VAD: {e}")
            self._vad = None
            self._initialized = False
    
    def is_speech(self, audio_frame: bytes) -> bool:
        """
        Detect if audio frame contains speech.

        Args:
            audio_frame: Audio frame bytes (16kHz, 16-bit, mono)

        Returns:
            bool: True if speech detected
        """
        if self._vad is None:
            return True  # Assume speech if VAD not available

        # Only add tracing occasionally to avoid performance impact
        # Use a simple counter to trace every 100th call
        if not hasattr(self, '_trace_counter'):
            self._trace_counter = 0

        self._trace_counter += 1
        should_trace = (self._trace_counter % 100 == 0)

        if should_trace:
            from cortexacommon import trace_span
            from opentelemetry import trace
            import time

            with trace_span("webrtc_vad.is_speech", {
                    "provider": "webrtc",
                    "aggressiveness": self.aggressiveness,
                    "frame_size_bytes": len(audio_frame),
                    "expected_frame_size": self.frame_size * 2,
                    "sample_rate": settings.audio_sample_rate,
                    "trace_sample": self._trace_counter
                }) as span:
                try:
                    # Ensure frame is correct size
                    if len(audio_frame) != self.frame_size * 2:  # 2 bytes per sample
                        if span:
                            span.set_attribute("vad.frame_size_error", True)
                            span.add_event("frame_size_mismatch")
                        return False

                    vad_start = time.time()
                    result = self._vad.is_speech(audio_frame, settings.audio_sample_rate)
                    vad_time = time.time() - vad_start

                    if span:
                        span.set_attribute("vad.is_speech", result)
                        span.set_attribute("vad.processing_time_ms", vad_time * 1000)
                        span.set_attribute("vad.frame_size_error", False)

                    return result

                except Exception as e:
                    if span:
                        span.set_status(trace.Status(trace.StatusCode.ERROR, str(e)))
                        span.record_exception(e)
                    logger.warning(f"WebRTC VAD error: {e}")
                    return True  # Assume speech on error
        else:
            # Fast path without tracing
            try:
                # Ensure frame is correct size
                if len(audio_frame) != self.frame_size * 2:  # 2 bytes per sample
                    return False

                return self._vad.is_speech(audio_frame, settings.audio_sample_rate)
            except Exception as e:
                logger.warning(f"WebRTC VAD error: {e}")
                return True  # Assume speech on error
    
    @property
    def frame_size(self) -> int:
        """Get the required frame size for VAD processing."""
        return self._frame_size
