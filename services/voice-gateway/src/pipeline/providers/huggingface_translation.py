"""
HuggingFace Translation provider implementation.
"""

import asyncio
import logging
from typing import Optional

from .base import BaseTranslationProvider
from ...core.config import settings
from ...monitoring.provider_monitor import provider_monitor

logger = logging.getLogger(__name__)


class HuggingFaceTranslationProvider(BaseTranslationProvider):
    """Translation provider using Hugging Face transformers."""
    
    def __init__(self, 
                 model: Optional[str] = None,
                 device: Optional[str] = None,
                 **kwargs):
        """
        Initialize HuggingFace Translation provider.
        
        Args:
            model: Translation model identifier
            device: Device for model (cpu, cuda)
            **kwargs: Additional configuration parameters
        """
        super().__init__(**kwargs)
        self.model = model or settings.translation_model
        self.device = device or settings.translation_device
        self._pipeline: Optional[object] = None
    
    async def initialize(self) -> None:
        """Initialize translation model."""
        try:
            from transformers import pipeline
            
            # Run model initialization in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            self._pipeline = await loop.run_in_executor(
                None,
                lambda: pipeline(
                    "translation",
                    model=self.model,
                    device=0 if self.device == "cuda" else -1,
                )
            )
            self._initialized = True
            logger.info(f"Translation model {self.model} initialized on {self.device}")
        except ImportError:
            logger.error("transformers not available")
            self._pipeline = None
            self._initialized = False
        except Exception as e:
            logger.error(f"Failed to initialize translation model: {e}")
            self._pipeline = None
            self._initialized = False
    
    async def translate(self, text: str) -> str:
        """
        Translate text to target language.

        Args:
            text: Text to translate

        Returns:
            Translated text
        """
        if not text.strip() or self._pipeline is None:
            return text

        from cortexacommon import trace_span
        from opentelemetry import trace
        import time

        with trace_span("huggingface_translation.translate", {
                "provider": "huggingface",
                "model": self.model,
                "device": self.device,
                "text_length": len(text),
                "text_preview": text[:50]
            }) as span:

            # Start provider monitoring
            overall_start = time.time()

            try:
                # Prepare for translation
                preparation_start = time.time()
                max_length = min(512, len(text) * 2)  # Adaptive max length
                preparation_time = time.time() - preparation_start

                # Run translation in thread pool to avoid blocking
                translation_start = time.time()
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    None,
                    lambda: self._pipeline(text, max_length=max_length)
                )
                translation_time = time.time() - translation_start

                # Process result
                processing_start = time.time()
                if result and len(result) > 0:
                    translated_text = result[0]['translation_text']
                    processing_time = time.time() - processing_start

                    if span:
                        span.set_attribute("translation.preparation_time_ms", preparation_time * 1000)
                        span.set_attribute("translation.model_time_ms", translation_time * 1000)
                        span.set_attribute("translation.processing_time_ms", processing_time * 1000)
                        span.set_attribute("translation.total_time_ms",
                                         (preparation_time + translation_time + processing_time) * 1000)
                        span.set_attribute("translation.source_length", len(text))
                        span.set_attribute("translation.target_length", len(translated_text))
                        span.set_attribute("translation.length_ratio", len(translated_text) / len(text))
                        span.set_attribute("translation.characters_per_second",
                                         len(text) / translation_time if translation_time > 0 else 0)
                        span.set_attribute("translation.max_length_used", max_length)
                        span.add_event("translation_completed", {
                            "success": True,
                            "result_count": len(result)
                        })

                    # Record successful provider request
                    total_time = time.time() - overall_start
                    # Estimate quality based on length ratio and processing time
                    quality_score = min(1.0, 0.8 + (len(translated_text) / len(text) * 0.2))

                    provider_monitor.record_request_success(
                        provider_type="translation",
                        provider_name="huggingface",
                        latency=total_time,
                        quality_score=quality_score,
                        data_size=len(text)
                    )

                    return translated_text
                else:
                    if span:
                        span.add_event("translation_no_result", {
                            "result_length": len(result) if result else 0
                        })

                    # Record as partial failure (no result but no exception)
                    total_time = time.time() - overall_start
                    provider_monitor.record_request_failure(
                        provider_type="translation",
                        provider_name="huggingface",
                        error_type="NoResult",
                        error_message="Translation returned no results"
                    )

                    return text

            except Exception as e:
                # Record failed provider request
                total_time = time.time() - overall_start
                provider_monitor.record_request_failure(
                    provider_type="translation",
                    provider_name="huggingface",
                    error_type=type(e).__name__,
                    error_message=str(e)
                )

                if span:
                    span.set_status(trace.Status(trace.StatusCode.ERROR, str(e)))
                    span.record_exception(e)
                    span.add_event("translation_failed", {
                        "error": str(e),
                        "error_type": type(e).__name__
                    })
                logger.error(f"HuggingFace translation error: {e}")
                return text
