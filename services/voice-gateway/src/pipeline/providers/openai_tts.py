"""
OpenAI TTS provider implementation.
"""

import logging
from typing import Optional

from .base import BaseTTSProvider
from ...core.config import settings
from ...monitoring.provider_monitor import provider_monitor

logger = logging.getLogger(__name__)


class OpenAITTSProvider(BaseTTSProvider):
    """Text-to-Speech provider using OpenAI TTS API."""
    
    def __init__(self, 
                 api_key: Optional[str] = None,
                 model: Optional[str] = None,
                 voice: Optional[str] = None,
                 **kwargs):
        """
        Initialize OpenAI TTS provider.
        
        Args:
            api_key: OpenAI API key
            model: TTS model identifier
            voice: Voice identifier
            **kwargs: Additional configuration parameters
        """
        super().__init__(**kwargs)
        self.api_key = api_key or settings.tts_api_key
        self.model = model or settings.tts_model
        self.voice = voice or settings.tts_voice
        self._client: Optional[object] = None
    
    async def initialize(self) -> None:
        """Initialize TTS client."""
        if not self.api_key:
            logger.warning("OpenAI API key not provided, TTS not configured")
            self._client = None
            self._initialized = False
            return
        
        try:
            import openai
            self._client = openai.AsyncOpenAI(api_key=self.api_key)
            self._initialized = True
            logger.info("OpenAI TTS client initialized")
        except ImportError:
            logger.error("openai package not available")
            self._client = None
            self._initialized = False
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI TTS client: {e}")
            self._client = None
            self._initialized = False
    
    async def synthesize(self, text: str) -> Optional[bytes]:
        """
        Synthesize speech from text.

        Args:
            text: Text to synthesize

        Returns:
            Audio bytes or None if synthesis fails
        """
        if not text.strip() or self._client is None:
            return None

        from cortexacommon import trace_span
        from opentelemetry import trace
        import time

        with trace_span("openai_tts.synthesize", {
                "provider": "openai",
                "model": self.model,
                "voice": self.voice,
                "text_length": len(text),
                "text_preview": text[:50]
            }) as span:
            try:
                # Prepare request and start monitoring
                request_start = time.time()
                overall_start = request_start

                response = await self._client.audio.speech.create(
                    model=self.model,
                    voice=self.voice,
                    input=text,
                    response_format="wav",
                )

                request_time = time.time() - request_start

                # Process response
                processing_start = time.time()
                audio_content = response.content
                processing_time = time.time() - processing_start

                if span:
                    span.set_attribute("openai.request_time_ms", request_time * 1000)
                    span.set_attribute("openai.processing_time_ms", processing_time * 1000)
                    span.set_attribute("openai.total_time_ms", (request_time + processing_time) * 1000)
                    span.set_attribute("openai.audio_size_bytes", len(audio_content))
                    span.set_attribute("openai.characters_per_second", len(text) / request_time if request_time > 0 else 0)
                    span.add_event("synthesis_completed", {
                        "audio_size": len(audio_content),
                        "success": True
                    })

                # Record successful provider request
                total_time = time.time() - overall_start
                # Estimate quality score based on audio size and text length ratio
                quality_score = min(1.0, len(audio_content) / (len(text) * 100))  # Rough estimate

                provider_monitor.record_request_success(
                    provider_type="tts",
                    provider_name="openai",
                    latency=total_time,
                    quality_score=quality_score,
                    data_size=len(text)
                )

                return audio_content

            except Exception as e:
                # Record failed provider request
                total_time = time.time() - overall_start
                provider_monitor.record_request_failure(
                    provider_type="tts",
                    provider_name="openai",
                    error_type=type(e).__name__,
                    error_message=str(e)
                )

                if span:
                    span.set_status(trace.Status(trace.StatusCode.ERROR, str(e)))
                    span.record_exception(e)
                    span.add_event("synthesis_failed", {
                        "error": str(e),
                        "error_type": type(e).__name__
                    })
                logger.error(f"OpenAI TTS error: {e}")
                return None
