"""
Whisper STT provider implementation.
"""

import asyncio
import logging
import numpy as np
import time
from typing import Tuple, Optional

from .base import BaseST<PERSON>rovider
from ...core.config import settings
from ...monitoring.provider_monitor import provider_monitor

logger = logging.getLogger(__name__)


class WhisperSTTProvider(BaseSTTProvider):
    """Speech-to-Text provider using Faster Whisper."""
    
    def __init__(self, 
                 model_size: Optional[str] = None,
                 compute_type: Optional[str] = None,
                 device: Optional[str] = None,
                 **kwargs):
        """
        Initialize Whisper STT provider.
        
        Args:
            model_size: Whisper model size (tiny, base, small, medium, large)
            compute_type: Compute type (int8, int16, float16, float32)
            device: Device for model (cpu, cuda)
            **kwargs: Additional configuration parameters
        """
        super().__init__(**kwargs)
        self.model_size = model_size or settings.whisper_model_size
        self.compute_type = compute_type or settings.whisper_compute_type
        self.device = device or settings.whisper_device
        self._model: Optional[object] = None
    
    async def initialize(self) -> None:
        """Initialize Faster Whisper model."""
        try:
            from faster_whisper import WhisperModel
            
            # Run model initialization in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            self._model = await loop.run_in_executor(
                None,
                lambda: WhisperModel(
                    self.model_size,
                    device=self.device,
                    compute_type=self.compute_type,
                )
            )
            self._initialized = True
            logger.info(f"Whisper model {self.model_size} initialized on {self.device}")
        except ImportError:
            logger.error("faster-whisper not available")
            self._model = None
            self._initialized = False
        except Exception as e:
            logger.error(f"Failed to initialize Whisper model: {e}")
            self._model = None
            self._initialized = False
    
    async def transcribe(self, audio_data: bytes) -> Tuple[str, float]:
        """
        Transcribe audio to text.

        Args:
            audio_data: Audio data bytes

        Returns:
            Tuple of (transcribed_text, confidence_score)
        """
        if self._model is None:
            return "Transcription not available", 0.0

        from cortexacommon import trace_span

        with trace_span("whisper_stt.transcribe", {
                "audio_size_bytes": len(audio_data),
                "model": self.model_size,
                "compute_type": self.compute_type,
                "device": self.device,
                "provider": "whisper"
            }) as span:

            # Start provider monitoring
            request_start = time.time()

            try:
                # Convert bytes to numpy array
                conversion_start = time.time()
                audio_np = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0
                conversion_time = time.time() - conversion_start

                audio_duration = len(audio_np) / 16000

                if span:
                    span.set_attribute("audio.duration_seconds", audio_duration)
                    span.set_attribute("audio.sample_rate", 16000)
                    span.set_attribute("audio.conversion_time_ms", conversion_time * 1000)
                    span.add_event("audio_converted")

                # Run transcription in thread pool to avoid blocking
                transcription_start = time.time()
                loop = asyncio.get_event_loop()
                segments, info = await loop.run_in_executor(
                    None,
                    lambda: self._model.transcribe(audio_np, language="en")
                )
                transcription_time = time.time() - transcription_start

                if span:
                    span.set_attribute("whisper.transcription_time_ms", transcription_time * 1000)
                    span.set_attribute("whisper.realtime_factor", transcription_time / audio_duration)
                    span.set_attribute("whisper.language", info.language if hasattr(info, 'language') else "en")
                    span.set_attribute("whisper.language_probability",
                                     getattr(info, 'language_probability', 0.0))
                    span.add_event("transcription_completed")

                # Combine segments into single text
                processing_start = time.time()
                text_parts = []
                total_confidence = 0.0
                segment_count = 0
                segment_details = []

                for segment in segments:
                    text_parts.append(segment.text.strip())
                    # Convert log probability to confidence score (0.0 to 1.0)
                    # avg_logprob is typically negative, so we use exp() to convert to probability
                    # and clamp to [0.0, 1.0] range
                    avg_logprob = getattr(segment, 'avg_logprob', -1.0)
                    segment_confidence = max(0.0, min(1.0, np.exp(avg_logprob)))
                    total_confidence += segment_confidence
                    segment_count += 1

                    # Collect segment details for tracing
                    segment_details.append({
                        "start": getattr(segment, 'start', 0.0),
                        "end": getattr(segment, 'end', 0.0),
                        "confidence": segment_confidence,
                        "text_length": len(segment.text.strip())
                    })

                text = " ".join(text_parts).strip()
                confidence = total_confidence / segment_count if segment_count > 0 else 0.0
                processing_time = time.time() - processing_start

                if span:
                    span.set_attribute("whisper.segment_count", segment_count)
                    span.set_attribute("whisper.text_length", len(text))
                    span.set_attribute("whisper.average_confidence", confidence)
                    span.set_attribute("whisper.processing_time_ms", processing_time * 1000)
                    span.set_attribute("whisper.total_time_ms",
                                     (conversion_time + transcription_time + processing_time) * 1000)

                if span:
                    span.set_attribute("transcription.text_length", len(text))
                    span.set_attribute("transcription.confidence", confidence)
                    span.set_attribute("transcription.segment_count", segment_count)

                # Record successful provider request
                total_time = time.time() - request_start
                provider_monitor.record_request_success(
                    provider_type="stt",
                    provider_name="whisper",
                    latency=total_time,
                    confidence=confidence,
                    data_size=len(audio_data)
                )

                return text, confidence

            except Exception as e:
                # Record failed provider request
                total_time = time.time() - request_start
                provider_monitor.record_request_failure(
                    provider_type="stt",
                    provider_name="whisper",
                    error_type=type(e).__name__,
                    error_message=str(e)
                )

                if span:
                    from opentelemetry import trace
                    span.set_status(trace.Status(trace.StatusCode.ERROR, str(e)))
                    span.record_exception(e)
                logger.error(f"Whisper STT error: {e}")
                return "", 0.0
