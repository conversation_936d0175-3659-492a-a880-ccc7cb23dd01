server:
  http_listen_port: 3200
  log_level: info

distributor:
  receivers:
    otlp:
      protocols:
        grpc:
          endpoint: 0.0.0.0:4317
        http:
          endpoint: 0.0.0.0:4318
  # Optimize for voice pipeline high-frequency traces
  ring:
    kvstore:
      store: inmemory

ingester:
  # Optimized for voice pipeline traces
  trace_idle_period: 5s  # Shorter idle period for real-time processing
  max_block_bytes: 2_000_000  # Larger blocks for better compression
  max_block_duration: 2m  # Shorter duration for faster queries
  lifecycler:
    ring:
      kvstore:
        store: inmemory

compactor:
  compaction:
    compaction_window: 30m  # More frequent compaction
    max_compaction_objects: 2000000
    block_retention: 2h  # Keep traces for 2 hours
    compacted_block_retention: 30m
  ring:
    kvstore:
      store: inmemory

storage:
  trace:
    backend: local
    local:
      path: /tmp/tempo/traces
    wal:
      path: /tmp/tempo/wal
    pool:
      max_workers: 200  # More workers for voice pipeline load
      queue_depth: 20000

# Query frontend for better performance
query_frontend:
  search:
    duration_slo: 5s
    throughput_bytes_slo: 1.073741824e+09
  trace_by_id:
    duration_slo: 5s

# Metrics generator for RED metrics from traces
metrics_generator:
  registry:
    external_labels:
      source: tempo
      cluster: voice-gateway
  storage:
    path: /tmp/tempo/generator/wal
    remote_write:
      - url: http://prometheus:9090/api/v1/write
        send_exemplars: true
  traces_storage:
    path: /tmp/tempo/generator/traces

# Overrides for voice pipeline optimization
overrides:
  # Global limits for all tenants
  ingestion_rate_strategy: global
  ingestion_rate_limit_bytes: 20000000  # 20MB/s
  ingestion_burst_size_bytes: 40000000  # 40MB burst
  max_traces_per_user: 50000
  max_global_traces_per_user: 100000
  # Optimize for voice pipeline span characteristics
  max_bytes_per_trace: 5000000  # 5MB per trace (voice calls can be long)
  max_search_bytes_per_trace: 1000000  # 1MB for search
