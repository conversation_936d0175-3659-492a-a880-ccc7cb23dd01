# Voice Gateway Monitoring Stack

This directory contains the complete observability stack for the Voice Gateway service, including metrics collection, distributed tracing, and visualization.

## Components

### 1. Prometheus (Metrics Collection)
- **Port**: 9090
- **Purpose**: Collects and stores time-series metrics from the voice-gateway service
- **Configuration**: `prometheus.yml`
- **Scrapes**: Voice gateway metrics every 5 seconds for real-time monitoring

### 2. Grafana (Visualization)
- **Port**: 3000
- **Purpose**: Visualizes metrics and traces through dashboards
- **Default Credentials**: admin/admin
- **Dashboards**: Pre-configured dashboards for voice-gateway monitoring

### 3. Tempo (Distributed Tracing)
- **Port**: 3200 (HTTP), 4317 (OTLP gRPC), 4318 (OTLP HTTP)
- **Purpose**: Collects and stores distributed traces from the voice pipeline
- **Configuration**: `tempo.yaml` - Optimized for voice pipeline characteristics
- **Why Tempo over Jaeger**: Better Grafana integration, modern architecture, OTLP native support
- **Voice Pipeline Optimizations**:
  - Shorter trace idle periods (5s) for real-time processing
  - Higher ingestion limits (20MB/s) for voice data volume
  - Larger trace size limits (5MB) for long voice calls
  - Metrics generator for RED metrics from traces

### 4. Node Exporter (System Metrics)
- **Port**: 9100
- **Purpose**: Collects system-level metrics (CPU, memory, disk, network)
- **Integration**: Automatically scraped by Prometheus

## Voice Pipeline Monitoring Features

### Enhanced Tracing
The voice gateway now includes comprehensive distributed tracing with detailed spans for:

- **VAD Processing**: Voice activity detection with timing and accuracy metrics
- **STT Pipeline**: Speech-to-text with provider-specific performance tracking
- **Translation**: Text translation with quality and latency metrics
- **TTS Pipeline**: Text-to-speech synthesis with audio quality tracking
- **Queue Operations**: Audio buffer and processing queue monitoring
- **Provider Switching**: Automatic failover and performance comparison

### Detailed Metrics
Custom metrics specific to voice processing:

- **Pipeline Latency**: End-to-end processing time with percentile analysis
- **Component Performance**: Individual latency for VAD, STT, Translation, TTS
- **Real-time Factors**: Processing speed compared to audio duration
- **Quality Metrics**: STT confidence, translation accuracy, TTS naturalness
- **Provider Comparison**: Performance ranking and automatic selection
- **Error Tracking**: Component-specific error rates and types
- **Queue Health**: Buffer sizes and wait times

### Provider Performance Monitoring
Advanced provider comparison and selection:

- **Performance Scoring**: Composite scores based on latency, quality, and reliability
- **Automatic Failover**: Switch providers when performance degrades
- **Cost Tracking**: Estimated costs per provider and request
- **Quality Assessment**: STT confidence, translation BLEU scores, TTS naturalness
- **Availability Monitoring**: Provider uptime and consecutive failure tracking
