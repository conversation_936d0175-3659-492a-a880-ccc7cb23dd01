# Voice Gateway Monitoring Stack

This directory contains the complete observability stack for the Voice Gateway service, including metrics collection, distributed tracing, and visualization.

## Components

### 1. Prometheus (Metrics Collection)
- **Port**: 9090
- **Purpose**: Collects and stores time-series metrics from the voice-gateway service
- **Configuration**: `prometheus.yml`

### 2. Grafana (Visualization)
- **Port**: 3000
- **Purpose**: Visualizes metrics and traces through dashboards
- **Default Credentials**: admin/admin
- **Dashboards**: Pre-configured dashboards for voice-gateway monitoring

### 3. <PERSON><PERSON><PERSON> (Distributed Tracing)
- **Port**: 16686 (UI), 14268 (Collector)
- **Purpose**: Collects and visualizes distributed traces from the S2ST pipeline
- **Integration**: OpenTelemetry traces from voice-gateway

### 4. Tempo (Alternative Tracing Backend)
- **Port**: 3200 (API), 4317 (OTLP gRPC), 4318 (OTLP HTTP)
- **Purpose**: Alternative to <PERSON><PERSON><PERSON> for trace storage and querying
- **Integration**: Works with Grafana for trace visualization

### 5. Node Exporter (System Metrics)
- **Port**: 9100
- **Purpose**: Collects system-level metrics (CPU, memory, disk, network)

### 6. Redis Exporter (Redis Metrics)
- **Port**: 9121
- **Purpose**: Collects Redis performance metrics
