version: '3.8'

services:


  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: voice-gateway-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - monitoring

  # Grafana for visualization
  grafana:
    image: grafana/grafana:10.1.0
    container_name: voice-gateway-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - monitoring

  # Tempo for trace storage (alternative to Jaeger)
  tempo:
    image: grafana/tempo:2.2.0
    container_name: voice-gateway-tempo
    command: [ "-config.file=/etc/tempo.yaml" ]
    volumes:
      - ./monitoring/tempo.yaml:/etc/tempo.yaml
      - tempo_data:/tmp/tempo
    ports:
      - "3200:3200"   # tempo
      - "4317:4317"   # otlp grpc
      - "4318:4318"   # otlp http
    networks:
      - monitoring

  # Node Exporter for system metrics
  node-exporter:
    image: prom/node-exporter:v1.6.1
    container_name: voice-gateway-node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - monitoring

  # # Redis Exporter for Redis metrics
  # redis-exporter:
  #   image: oliver006/redis_exporter:v1.54.0
  #   container_name: voice-gateway-redis-exporter
  #   ports:
  #     - "9121:9121"
  #   environment:
  #     - REDIS_ADDR=redis://host.docker.internal:6379
  #   networks:
  #     - monitoring

volumes:
  prometheus_data:
  grafana_data:
  tempo_data:

networks:
  monitoring:
    driver: bridge
